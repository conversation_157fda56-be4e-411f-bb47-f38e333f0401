// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Stats/Stats.h"

// You should place include statements to your module's private header files here.  You only need to
// add includes for headers that are used in most of your module's source files though.


DECLARE_STATS_GROUP(TEXT("ChaosModularVehicle"), STATGROUP_ChaosModularVehicle, STATCAT_Advanced);
