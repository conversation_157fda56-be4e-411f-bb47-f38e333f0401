<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>OpenEXR</Name>
  <Location>Engine/Source/ThirdParty/openexr</Location>
  <Function>OpenEXR provides the specification and reference implementation of the EXR file format, the professional-grade image storage format of the motion picture industry. It is a core component of the software specification for the VFX reference platform.</Function>
  <Eula>https://github.com/AcademySoftwareFoundation/openexr/blob/v3.3.2/LICENSE.md</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>
