<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>mimalloc</Name>
  <!-- Software Name and Version  -->
<!-- Software Name: mimalloc
    Version: v2.1.2 -->
  <Location>Engine/Source/Thirdparty/mimalloc</Location>
  <Function>Configurable memory allocator, used by UE4 Core library as an option for better performance and lower memory usage.C code, statically linked with Core library.</Function>
  <Eula>https://github.com/microsoft/mimalloc/blob/master/LICENSE</Eula>
  <RedistributeTo>
    <EndUserGroup>Licencees</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>
 