<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name> Rivermax SDK</Name>
  <Location> Engine\Source\ThirdParty\NVIDIA\Rivermax</Location>
  <Function>Rivermax is a Nvidia sdk to use network cards optimally for Media and Streaming applications. Use cases will be for broadcast and media and entertainment mostly. </Function>
  <Eula> https://developer.nvidia.com/sites/default/files/akamai/networking/rivermax/NVIDIA-RIVERMAX-SOFTWARE-LICENSE-AGREEMENT-v19-NOV-2021.pdf </Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder></LicenseFolder>
</TpsData>