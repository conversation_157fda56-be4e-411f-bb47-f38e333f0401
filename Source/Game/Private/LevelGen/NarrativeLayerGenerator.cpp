#include "LevelGen/NarrativeLayerGenerator.h"
#include "Engine/Engine.h"
#include "LevelGen/MapUtil.h"
#include "Math/UnrealMathUtility.h"

UNarrativeLayerGenerator::UNarrativeLayerGenerator()
{
}

void UNarrativeLayerGenerator::GenerateNarrativeLayers(TArray<FMapCell>& MapCells, int32 Width, int32 Height, 
                                                      const FNarrativeGenerationParams& Params)
{
    // 设置随机种子
    FMath::RandInit(Params.Seed);
    
    // 1. 生成派系控制区域
    GenerateFactionControl(MapCells, Width, Height, Params);
    
    // 2. 计算派系间冲突区域
    CalculateConflictZones(MapCells, Width, Height, Params.Factions);
    
    // 3. 生成历史年代分布
    GenerateHistoricalAges(MapCells, Width, Height, Params);
    
    // 4. 计算威胁等级
    CalculateThreatLevels(MapCells, Width, Height, Params);
    
    // 5. 生成宗教分布（如果启用）
    if (Params.bEnableReligion)
    {
        GenerateReligionDistribution(MapCells, Width, Height, Params.Factions);
    }
}

void UNarrativeLayerGenerator::GenerateFactionControl(TArray<FMapCell>& MapCells, int32 Width, int32 Height, 
                                                     const FNarrativeGenerationParams& Params)
{
    // 初始化所有格子为无派系控制
    for (FMapCell& Cell : MapCells)
    {
        Cell.FactionID = 0;
        Cell.ControlStrength = 0.0f;
    }
    
    // 为每个派系选择起始点并扩展领土
    for (const FFactionInfo& Faction : Params.Factions)
    {
        // 寻找适合该派系的起始位置
        FIntPoint BestStartPoint = FIntPoint::ZeroValue;
        float BestSuitability = -1.0f;
        
        // 随机采样多个位置，选择最适合的
        for (int32 Attempt = 0; Attempt < 50; ++Attempt)
        {
            int32 X = FMath::RandRange(Width / 4, Width * 3 / 4);
            int32 Y = FMath::RandRange(Height / 4, Height * 3 / 4);
            int32 Index = GetCellIndex(X, Y, Width);
            
            if (Index >= 0 && Index < MapCells.Num())
            {
                float Suitability = CalculateFactionSuitability(MapCells[Index], Faction);
                
                // 确保与其他派系有一定距离
                bool bTooClose = false;
                for (int32 CheckY = Y - 20; CheckY <= Y + 20 && !bTooClose; ++CheckY)
                {
                    for (int32 CheckX = X - 20; CheckX <= X + 20 && !bTooClose; ++CheckX)
                    {
                        if (IsValidCoordinate(CheckX, CheckY, Width, Height))
                        {
                            int32 CheckIndex = GetCellIndex(CheckX, CheckY, Width);
                            if (CheckIndex >= 0 && CheckIndex < MapCells.Num() && 
                                MapCells[CheckIndex].FactionID != 0 && 
                                MapCells[CheckIndex].FactionID != Faction.FactionID)
                            {
                                bTooClose = true;
                            }
                        }
                    }
                }
                
                if (!bTooClose && Suitability > BestSuitability)
                {
                    BestSuitability = Suitability;
                    BestStartPoint = FIntPoint(X, Y);
                }
            }
        }
        
        // 从最佳起始点扩展领土
        if (BestSuitability > 0.0f)
        {
            float TerritoryRadius = 30.0f * Faction.Power * Params.FactionDensity;
            ExpandFactionTerritory(MapCells, Width, Height, Faction, BestStartPoint, TerritoryRadius);
        }
    }
}

void UNarrativeLayerGenerator::CalculateConflictZones(TArray<FMapCell>& MapCells, int32 Width, int32 Height, 
                                                     const TArray<FFactionInfo>& Factions)
{
    // 创建派系关系映射
    TMap<int32, FFactionInfo> FactionMap;
    for (const FFactionInfo& Faction : Factions)
    {
        FactionMap.Add(Faction.FactionID, Faction);
    }
    
    for (int32 Y = 0; Y < Height; ++Y)
    {
        for (int32 X = 0; X < Width; ++X)
        {
            int32 Index = GetCellIndex(X, Y, Width);
            if (Index < 0 || Index >= MapCells.Num()) continue;
            
            FMapCell& Cell = MapCells[Index];
            
            // 检查邻居格子的派系控制
            TMap<int32, float> NeighborFactions;
            TArray<FIntPoint> Neighbors = UMapUtil::GetEightNeighbors(X, Y);
            
            for (const FIntPoint& Neighbor : Neighbors)
            {
                if (IsValidCoordinate(Neighbor.X, Neighbor.Y, Width, Height))
                {
                    int32 NeighborIndex = GetCellIndex(Neighbor.X, Neighbor.Y, Width);
                    if (NeighborIndex >= 0 && NeighborIndex < MapCells.Num())
                    {
                        const FMapCell& NeighborCell = MapCells[NeighborIndex];
                        if (NeighborCell.FactionID != 0)
                        {
                            if (NeighborFactions.Contains(NeighborCell.FactionID))
                            {
                                NeighborFactions[NeighborCell.FactionID] += NeighborCell.ControlStrength;
                            }
                            else
                            {
                                NeighborFactions.Add(NeighborCell.FactionID, NeighborCell.ControlStrength);
                            }
                        }
                    }
                }
            }
            
            // 如果有多个敌对派系在附近，创建冲突区域
            if (NeighborFactions.Num() > 1)
            {
                TArray<int32> HostileFactions;
                
                for (const auto& FactionPair1 : NeighborFactions)
                {
                    for (const auto& FactionPair2 : NeighborFactions)
                    {
                        if (FactionPair1.Key != FactionPair2.Key)
                        {
                            const FFactionInfo* Faction1 = FactionMap.Find(FactionPair1.Key);
                            const FFactionInfo* Faction2 = FactionMap.Find(FactionPair2.Key);
                            
                            if (Faction1 && Faction2)
                            {
                                const EFactionRelation* Relation = Faction1->Relations.Find(FactionPair2.Key);
                                if (Relation && *Relation == EFactionRelation::Hostile)
                                {
                                    HostileFactions.AddUnique(FactionPair1.Key);
                                    HostileFactions.AddUnique(FactionPair2.Key);
                                }
                            }
                        }
                    }
                }
                
                // 如果存在敌对派系，降低控制力并增加威胁
                if (HostileFactions.Num() > 1)
                {
                    Cell.ControlStrength *= 0.5f; // 冲突区域控制力减半
                    
                    // 如果当前格子属于某个派系，可能会失去控制
                    if (Cell.FactionID != 0 && FMath::RandRange(0.0f, 1.0f) < 0.3f)
                    {
                        Cell.FactionID = 0;
                        Cell.ControlStrength = 0.0f;
                    }
                }
            }
        }
    }
}

void UNarrativeLayerGenerator::CalculateThreatLevels(TArray<FMapCell>& MapCells, int32 Width, int32 Height, 
                                                    const FNarrativeGenerationParams& Params)
{
    for (FMapCell& Cell : MapCells)
    {
        float ThreatLevel = 0.0f;
        
        // 地理威胁
        ThreatLevel += CalculateGeographicThreat(Cell) * 0.4f;
        
        // 历史威胁
        ThreatLevel += CalculateHistoricalThreat(Cell) * 0.3f;
        
        // 派系威胁
        ThreatLevel += CalculateFactionThreat(Cell, Params.Factions) * 0.3f;
        
        // 应用威胁变化幅度
        float Variation = (FMath::RandRange(-1.0f, 1.0f) * Params.ThreatVariation * 0.2f);
        ThreatLevel += Variation;
        
        Cell.ThreatLevel = FMath::Clamp(ThreatLevel, 0.0f, 1.0f);
    }
}

TArray<FThreatPoint> UNarrativeLayerGenerator::GenerateThreatPoints(const TArray<FMapCell>& MapCells, int32 Width, int32 Height, 
                                                                   const FNarrativeGenerationParams& Params)
{
    TArray<FThreatPoint> ThreatPoints;
    
    // 基于威胁等级生成威胁点
    for (int32 Y = 0; Y < Height; ++Y)
    {
        for (int32 X = 0; X < Width; ++X)
        {
            int32 Index = GetCellIndex(X, Y, Width);
            if (Index < 0 || Index >= MapCells.Num()) continue;
            
            const FMapCell& Cell = MapCells[Index];
            
            // 高威胁区域有概率生成威胁点
            if (Cell.ThreatLevel > 0.7f && FMath::RandRange(0.0f, 1.0f) < 0.1f)
            {
                FThreatPoint ThreatPoint;
                ThreatPoint.Position = FIntPoint(X, Y);
                ThreatPoint.ThreatLevel = Cell.ThreatLevel;
                ThreatPoint.InfluenceRadius = FMath::RandRange(3.0f, 8.0f);
                
                // 根据环境确定威胁来源
                if (Cell.Age > 1000)
                {
                    ThreatPoint.ThreatSource = EThreatSource::Ancient;
                    ThreatPoint.Description = TEXT("Ancient Guardian");
                }
                else if (Cell.Corruption > 0.5f)
                {
                    ThreatPoint.ThreatSource = EThreatSource::Corruption;
                    ThreatPoint.Description = TEXT("Corrupted Zone");
                }
                else if (Cell.FactionID == 0 && Cell.ThreatLevel > 0.8f)
                {
                    ThreatPoint.ThreatSource = EThreatSource::Bandits;
                    ThreatPoint.Description = TEXT("Bandit Camp");
                }
                else
                {
                    // 根据生态区确定威胁类型
                    EBiomeType DominantBiome = Cell.BiomeWeights.GetDominantBiome();
                    switch (DominantBiome)
                    {
                        case EBiomeType::Volcano:
                        case EBiomeType::Desert:
                        case EBiomeType::Swamp:
                            ThreatPoint.ThreatSource = EThreatSource::Natural;
                            ThreatPoint.Description = TEXT("Natural Hazard");
                            break;
                        default:
                            ThreatPoint.ThreatSource = EThreatSource::Wildlife;
                            ThreatPoint.Description = TEXT("Dangerous Wildlife");
                            break;
                    }
                }
                
                ThreatPoints.Add(ThreatPoint);
            }
        }
    }
    
    return ThreatPoints;
}

void UNarrativeLayerGenerator::GenerateHistoricalAges(TArray<FMapCell>& MapCells, int32 Width, int32 Height, 
                                                     const FNarrativeGenerationParams& Params)
{
    // 使用噪声生成历史年代分布
    for (int32 Y = 0; Y < Height; ++Y)
    {
        for (int32 X = 0; X < Width; ++X)
        {
            int32 Index = GetCellIndex(X, Y, Width);
            if (Index < 0 || Index >= MapCells.Num()) continue;
            
            FMapCell& Cell = MapCells[Index];
            
            // 基础年代噪声
            float NoiseValue = FMath::PerlinNoise2D(FVector2D(X * 0.01f, Y * 0.01f));
            NoiseValue = (NoiseValue + 1.0f) * 0.5f; // 归一化到[0,1]
            
            // 高海拔和偏远地区倾向于更古老
            float HeightFactor = Cell.ZLevel / 1000.0f;
            float RemotenessFactor = 1.0f - Cell.ControlStrength; // 控制力低的地方更偏远
            
            float AgeFactor = (NoiseValue * 0.5f + HeightFactor * 0.3f + RemotenessFactor * 0.2f);
            
            // 应用历史密度参数
            AgeFactor *= Params.HistoricalDensity * 2.0f;
            
            Cell.Age = static_cast<int32>(AgeFactor * Params.MaxAge);
            Cell.Age = FMath::Max(Cell.Age, 0);
        }
    }
}

TArray<FHistoricalSite> UNarrativeLayerGenerator::GenerateHistoricalSites(const TArray<FMapCell>& MapCells, int32 Width, int32 Height,
                                                                          const FNarrativeGenerationParams& Params)
{
    TArray<FHistoricalSite> HistoricalSites;

    // 寻找高年代值的区域生成历史遗迹
    for (int32 Y = 0; Y < Height; ++Y)
    {
        for (int32 X = 0; X < Width; ++X)
        {
            int32 Index = GetCellIndex(X, Y, Width);
            if (Index < 0 || Index >= MapCells.Num()) continue;

            const FMapCell& Cell = MapCells[Index];

            // 年代越久远，生成遗迹的概率越高
            float SiteChance = (static_cast<float>(Cell.Age) / Params.MaxAge) * Params.HistoricalDensity;

            if (Cell.Age > 500 && FMath::RandRange(0.0f, 1.0f) < SiteChance * 0.05f)
            {
                FHistoricalSite Site;
                Site.Position = FIntPoint(X, Y);
                Site.Age = Cell.Age;
                Site.Era = GetHistoricalEra(Cell.Age);
                Site.Importance = FMath::Clamp(SiteChance, 0.1f, 1.0f);

                // 根据年代和环境确定遗迹类型
                switch (Site.Era)
                {
                    case EHistoricalEra::Prehistoric:
                        Site.SiteName = TEXT("Prehistoric Monument");
                        Site.Description = TEXT("Ancient stone circles from a forgotten age");
                        break;
                    case EHistoricalEra::Ancient:
                        Site.SiteName = TEXT("Ancient Temple");
                        Site.Description = TEXT("Ruins of a once-mighty civilization");
                        break;
                    case EHistoricalEra::Medieval:
                        Site.SiteName = TEXT("Old Fortress");
                        Site.Description = TEXT("Crumbling walls of a medieval stronghold");
                        break;
                    case EHistoricalEra::Recent:
                        Site.SiteName = TEXT("Abandoned Settlement");
                        Site.Description = TEXT("Recently abandoned buildings");
                        break;
                    default:
                        Site.SiteName = TEXT("Historical Site");
                        break;
                }

                // 如果该区域有派系控制，设置原始建造者
                if (Cell.FactionID != 0)
                {
                    Site.OriginalFactionID = Cell.FactionID;
                }

                HistoricalSites.Add(Site);
            }
        }
    }

    return HistoricalSites;
}

void UNarrativeLayerGenerator::GenerateReligionDistribution(TArray<FMapCell>& MapCells, int32 Width, int32 Height,
                                                           const TArray<FFactionInfo>& Factions)
{
    // 简化的宗教系统：每个派系对应一个宗教ID
    for (FMapCell& Cell : MapCells)
    {
        if (Cell.FactionID != 0)
        {
            // 派系控制区域通常信仰该派系的宗教
            Cell.ReligionID = Cell.FactionID;

            // 但有小概率信仰其他宗教（宗教多样性）
            if (FMath::RandRange(0.0f, 1.0f) < 0.1f)
            {
                Cell.ReligionID = FMath::RandRange(1, Factions.Num());
            }
        }
        else
        {
            // 无派系控制的区域可能有原始信仰或无信仰
            if (FMath::RandRange(0.0f, 1.0f) < 0.3f)
            {
                Cell.ReligionID = FMath::RandRange(1, Factions.Num());
            }
            else
            {
                Cell.ReligionID = 0; // 无信仰
            }
        }
    }
}

FNarrativeGenerationParams UNarrativeLayerGenerator::GetDefaultNarrativeParams()
{
    FNarrativeGenerationParams Params;
    Params.Factions = GetDefaultFactions();
    return Params;
}

TArray<FFactionInfo> UNarrativeLayerGenerator::GetDefaultFactions()
{
    TArray<FFactionInfo> Factions;

    // 派系1：森林王国
    FFactionInfo ForestKingdom;
    ForestKingdom.FactionID = 1;
    ForestKingdom.FactionName = TEXT("Forest Kingdom");
    ForestKingdom.FactionColor = FLinearColor::Green;
    ForestKingdom.Power = 0.7f;
    ForestKingdom.Aggression = 0.3f;
    ForestKingdom.Expansion = 0.4f;
    ForestKingdom.PreferredBiomes.Add(EBiomeType::Forest);
    ForestKingdom.PreferredBiomes.Add(EBiomeType::Grassland);
    Factions.Add(ForestKingdom);

    // 派系2：沙漠部落
    FFactionInfo DesertTribes;
    DesertTribes.FactionID = 2;
    DesertTribes.FactionName = TEXT("Desert Tribes");
    DesertTribes.FactionColor = FLinearColor::Yellow;
    DesertTribes.Power = 0.5f;
    DesertTribes.Aggression = 0.6f;
    DesertTribes.Expansion = 0.7f;
    DesertTribes.PreferredBiomes.Add(EBiomeType::Desert);
    DesertTribes.PreferredBiomes.Add(EBiomeType::Grassland);
    Factions.Add(DesertTribes);

    // 派系3：山地氏族
    FFactionInfo MountainClans;
    MountainClans.FactionID = 3;
    MountainClans.FactionName = TEXT("Mountain Clans");
    MountainClans.FactionColor = FLinearColor::Gray;
    MountainClans.Power = 0.6f;
    MountainClans.Aggression = 0.4f;
    MountainClans.Expansion = 0.2f;
    MountainClans.PreferredBiomes.Add(EBiomeType::Mountain);
    MountainClans.PreferredBiomes.Add(EBiomeType::Tundra);
    Factions.Add(MountainClans);

    // 派系4：沼泽巫师
    FFactionInfo SwampWizards;
    SwampWizards.FactionID = 4;
    SwampWizards.FactionName = TEXT("Swamp Wizards");
    SwampWizards.FactionColor = FLinearColor(0.5f, 0.2f, 0.8f, 1.0f); // 紫色
    SwampWizards.Power = 0.4f;
    SwampWizards.Aggression = 0.2f;
    SwampWizards.Expansion = 0.3f;
    SwampWizards.PreferredBiomes.Add(EBiomeType::Swamp);
    SwampWizards.PreferredBiomes.Add(EBiomeType::Lake);
    Factions.Add(SwampWizards);

    // 派系5：海盗联盟
    FFactionInfo PirateAlliance;
    PirateAlliance.FactionID = 5;
    PirateAlliance.FactionName = TEXT("Pirate Alliance");
    PirateAlliance.FactionColor = FLinearColor::Blue;
    PirateAlliance.Power = 0.5f;
    PirateAlliance.Aggression = 0.8f;
    PirateAlliance.Expansion = 0.6f;
    PirateAlliance.PreferredBiomes.Add(EBiomeType::Ocean);
    PirateAlliance.PreferredBiomes.Add(EBiomeType::Lake);
    PirateAlliance.PreferredBiomes.Add(EBiomeType::River);
    Factions.Add(PirateAlliance);

    return Factions;
}

// 私有辅助函数实现
float UNarrativeLayerGenerator::CalculateFactionSuitability(const FMapCell& Cell, const FFactionInfo& Faction)
{
    float Suitability = 0.0f;

    // 检查偏好生态区
    EBiomeType DominantBiome = Cell.BiomeWeights.GetDominantBiome();
    if (Faction.PreferredBiomes.Contains(DominantBiome))
    {
        Suitability += 0.5f;

        // 如果生态区权重很高，额外加分
        if (Cell.BiomeWeights.Weights.Contains(DominantBiome))
        {
            Suitability += Cell.BiomeWeights.Weights[DominantBiome] * 0.3f;
        }
    }

    // 资源丰富度影响
    Suitability += Cell.Fertility * 0.2f;
    Suitability += Cell.MineralRichness * 0.2f;

    // 地理位置影响
    float HeightFactor = Cell.ZLevel / 1000.0f;
    if (Faction.PreferredBiomes.Contains(EBiomeType::Mountain))
    {
        Suitability += HeightFactor * 0.3f; // 山地派系喜欢高海拔
    }
    else
    {
        Suitability += (1.0f - HeightFactor) * 0.2f; // 其他派系偏好低海拔
    }

    // 水源影响
    float WaterLevel = Cell.GetEffectiveWaterLevel();
    if (Faction.PreferredBiomes.Contains(EBiomeType::Ocean) ||
        Faction.PreferredBiomes.Contains(EBiomeType::Lake) ||
        Faction.PreferredBiomes.Contains(EBiomeType::River))
    {
        Suitability += WaterLevel * 0.3f; // 水系派系需要水源
    }
    else if (Faction.PreferredBiomes.Contains(EBiomeType::Desert))
    {
        Suitability += (1.0f - WaterLevel) * 0.2f; // 沙漠派系适应干旱
    }

    // 污染和腐化的负面影响
    Suitability -= Cell.Pollution * 0.3f;
    Suitability -= Cell.Corruption * 0.5f;

    return FMath::Clamp(Suitability, 0.0f, 1.0f);
}

void UNarrativeLayerGenerator::ExpandFactionTerritory(TArray<FMapCell>& MapCells, int32 Width, int32 Height,
                                                     const FFactionInfo& Faction, const FIntPoint& StartPoint, float MaxRadius)
{
    // 使用广度优先搜索扩展领土
    TQueue<FIntPoint> ExpansionQueue;
    TSet<FIntPoint> Visited;

    ExpansionQueue.Enqueue(StartPoint);
    Visited.Add(StartPoint);

    // 设置起始点
    int32 StartIndex = GetCellIndex(StartPoint.X, StartPoint.Y, Width);
    if (StartIndex >= 0 && StartIndex < MapCells.Num())
    {
        MapCells[StartIndex].FactionID = Faction.FactionID;
        MapCells[StartIndex].ControlStrength = 1.0f;
    }

    while (!ExpansionQueue.IsEmpty())
    {
        FIntPoint CurrentPoint;
        ExpansionQueue.Dequeue(CurrentPoint);

        float DistanceFromStart = FMath::Sqrt(static_cast<float>(FMath::Square(CurrentPoint.X - StartPoint.X)) +
                                             FMath::Square(CurrentPoint.Y - StartPoint.Y));

        if (DistanceFromStart >= MaxRadius) continue;

        // 检查四邻域
        TArray<FIntPoint> Neighbors = UMapUtil::GetFourNeighbors(CurrentPoint.X, CurrentPoint.Y);

        for (const FIntPoint& Neighbor : Neighbors)
        {
            if (!IsValidCoordinate(Neighbor.X, Neighbor.Y, Width, Height) || Visited.Contains(Neighbor))
                continue;

            int32 NeighborIndex = GetCellIndex(Neighbor.X, Neighbor.Y, Width);
            if (NeighborIndex < 0 || NeighborIndex >= MapCells.Num()) continue;

            FMapCell& NeighborCell = MapCells[NeighborIndex];

            // 如果已被其他派系控制，跳过
            if (NeighborCell.FactionID != 0 && NeighborCell.FactionID != Faction.FactionID)
                continue;

            // 计算扩展概率
            float Suitability = CalculateFactionSuitability(NeighborCell, Faction);
            float DistanceFactor = 1.0f - (DistanceFromStart / MaxRadius);
            float ExpansionChance = Suitability * DistanceFactor * Faction.Expansion;

            if (FMath::RandRange(0.0f, 1.0f) < ExpansionChance)
            {
                NeighborCell.FactionID = Faction.FactionID;
                NeighborCell.ControlStrength = ExpansionChance;

                ExpansionQueue.Enqueue(Neighbor);
            }

            Visited.Add(Neighbor);
        }
    }
}

float UNarrativeLayerGenerator::CalculateGeographicThreat(const FMapCell& Cell)
{
    float Threat = 0.0f;

    // 高海拔威胁
    float HeightFactor = Cell.ZLevel / 1000.0f;
    if (HeightFactor > 0.7f)
    {
        Threat += (HeightFactor - 0.7f) * 0.5f;
    }

    // 极端温度威胁
    float EffectiveTemp = Cell.GetEffectiveTemperature();
    if (EffectiveTemp < 0.2f || EffectiveTemp > 0.8f)
    {
        Threat += 0.3f;
    }

    // 生态区威胁
    EBiomeType DominantBiome = Cell.BiomeWeights.GetDominantBiome();
    switch (DominantBiome)
    {
        case EBiomeType::Volcano:
            Threat += 0.8f;
            break;
        case EBiomeType::Desert:
            Threat += 0.4f;
            break;
        case EBiomeType::Swamp:
            Threat += 0.3f;
            break;
        case EBiomeType::Mountain:
            Threat += 0.2f;
            break;
        default:
            break;
    }

    // 污染和腐化威胁
    Threat += Cell.Pollution * 0.4f;
    Threat += Cell.Corruption * 0.6f;

    return FMath::Clamp(Threat, 0.0f, 1.0f);
}

float UNarrativeLayerGenerator::CalculateHistoricalThreat(const FMapCell& Cell)
{
    float Threat = 0.0f;

    // 年代越久远，潜在威胁越大
    if (Cell.Age > 1000)
    {
        float AgeFactor = FMath::Min(static_cast<float>(Cell.Age) / 5000.0f, 1.0f);
        Threat += AgeFactor * 0.5f;
    }

    // 古老且无人控制的区域更危险
    if (Cell.Age > 2000 && Cell.FactionID == 0)
    {
        Threat += 0.3f;
    }

    return FMath::Clamp(Threat, 0.0f, 1.0f);
}

float UNarrativeLayerGenerator::CalculateFactionThreat(const FMapCell& Cell, const TArray<FFactionInfo>& Factions, int32 PlayerFactionID)
{
    float Threat = 0.0f;

    // 无派系控制的区域可能有强盗
    if (Cell.FactionID == 0)
    {
        Threat += 0.2f;
    }
    else if (Cell.FactionID != PlayerFactionID)
    {
        // 敌对派系威胁
        for (const FFactionInfo& Faction : Factions)
        {
            if (Faction.FactionID == Cell.FactionID)
            {
                Threat += Faction.Aggression * Cell.ControlStrength * 0.4f;
                break;
            }
        }
    }

    return FMath::Clamp(Threat, 0.0f, 1.0f);
}

EHistoricalEra UNarrativeLayerGenerator::GetHistoricalEra(int32 Age)
{
    if (Age < 100) return EHistoricalEra::Modern;
    if (Age < 500) return EHistoricalEra::Recent;
    if (Age < 1000) return EHistoricalEra::Medieval;
    if (Age < 3000) return EHistoricalEra::Ancient;
    return EHistoricalEra::Prehistoric;
}

void UNarrativeLayerGenerator::GenerateFactionRelations(TArray<FFactionInfo>& Factions)
{
    // 为每对派系生成关系
    for (int32 i = 0; i < Factions.Num(); ++i)
    {
        for (int32 j = i + 1; j < Factions.Num(); ++j)
        {
            FFactionInfo& Faction1 = Factions[i];
            FFactionInfo& Faction2 = Factions[j];

            // 基于派系特性决定关系
            float RelationScore = 0.5f;

            // 侵略性高的派系更容易敌对
            RelationScore -= (Faction1.Aggression + Faction2.Aggression) * 0.3f;

            // 扩张欲望高的派系更容易冲突
            RelationScore -= (Faction1.Expansion + Faction2.Expansion) * 0.2f;

            // 添加随机因素
            RelationScore += FMath::RandRange(-0.3f, 0.3f);

            EFactionRelation Relation;
            if (RelationScore < 0.2f)
            {
                Relation = EFactionRelation::Hostile;
            }
            else if (RelationScore > 0.7f)
            {
                Relation = EFactionRelation::Allied;
            }
            else if (RelationScore > 0.5f)
            {
                Relation = EFactionRelation::Trade;
            }
            else
            {
                Relation = EFactionRelation::Neutral;
            }

            // 设置双向关系
            Faction1.Relations.Add(Faction2.FactionID, Relation);
            Faction2.Relations.Add(Faction1.FactionID, Relation);
        }
    }
}

int32 UNarrativeLayerGenerator::GetCellIndex(int32 X, int32 Y, int32 Width)
{
    return Y * Width + X;
}

bool UNarrativeLayerGenerator::IsValidCoordinate(int32 X, int32 Y, int32 Width, int32 Height)
{
    return X >= 0 && X < Width && Y >= 0 && Y < Height;
}
